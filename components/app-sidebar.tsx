"use client";
import * as React from "react";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarContent,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { useSession } from "@/lib/auth-client";
import Link from "next/link";
import {
  LayoutDashboard,
  FileText,
  Video,
  Image as ImageIcon,
  Folder,
} from "lucide-react";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const session = useSession();
  const userId = session?.data?.user?.id;
  // Update navItems data with dynamic routes and icons
  const navItems = [
    {
      title: "Dashboard",
      url: `/user/${userId}`,
      icon: <LayoutDashboard className="mr-2 h-5 w-5" />,
    },
    {
      title: "Documents",
      url: `/user/${userId}/documents`,
      icon: <FileText className="mr-2 h-5 w-5" />,
    },
    {
      title: "Images",
      url: `/user/${userId}/images`,
      icon: <ImageIcon className="mr-2 h-5 w-5" />,
    },
    {
      title: "Media",
      url: `/user/${userId}/media`,
      icon: <Video className="mr-2 h-5 w-5" />,
    },
    {
      title: "Others",
      url: `/user/${userId}/others`,
      icon: <Folder className="mr-2 h-5 w-5" />,
    },
  ];

  return (
    <Sidebar
      {...props}
      className="min-h-screen w-64 border-r border-gray-200 bg-white"
    >
      <SidebarHeader className="border-b border-gray-100 p-6">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
            <svg
              className="h-5 w-5 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"
              />
            </svg>
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">SafeSpace</h1>
            <p className="text-xs text-gray-500">File Storage</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="p-4">
        <SidebarGroupContent>
          <SidebarMenu className="space-y-1">
            {navItems.map((item) => {
              const isActive = pathname.includes(item.title.toLowerCase());
              return (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive}
                    className={`flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                      isActive
                        ? "bg-blue-50 text-blue-700"
                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    }`}
                  >
                    <Link href={item.url} className="flex w-full items-center">
                      <div className="mr-3">
                        {React.cloneElement(item.icon, {
                          className: `h-5 w-5 ${isActive ? "text-blue-700" : "text-gray-500"}`,
                        })}
                      </div>
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarContent>

      <SidebarRail />

      <SidebarFooter className="border-t border-gray-100 p-4">
        {/* User Info Section */}
        <div className="mb-4 rounded-lg border border-gray-200 bg-gray-50 p-3">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-xs font-semibold text-white">
              {session?.data?.user?.name?.charAt(0) || "U"}
            </div>
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm font-medium text-gray-900">
                {session?.data?.user?.name || "User"}
              </p>
              <p className="truncate text-xs text-gray-500">
                {session?.data?.user?.email || "<EMAIL>"}
              </p>
            </div>
          </div>
        </div>

        <div className="text-center text-xs text-gray-400">
          © {new Date().getFullYear()} SafeSpace
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
