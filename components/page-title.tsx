import React from "react";
import { Combobox } from "./ui/combo-box";

const PageTitle = ({ title, size }: { title: string; size: number }) => {
  return (
    <div className="mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
      <div>
        <h2 className="mb-1 text-2xl font-bold text-gray-900">{title}</h2>
        <p className="text-sm text-gray-600">
          Total size: <span className="font-medium">{size.toFixed(2)} MB</span>
        </p>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">Sort by:</span>
        <Combobox />
      </div>
    </div>
  );
};

export default PageTitle;
