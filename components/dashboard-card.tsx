import { fileType } from "@/app/types";
import { getFileIconName, getFileColor } from "@/lib/utils";
import { FileInfo } from "@/app/types";
import { getFileSize } from "@/lib/utils";
import { ImageIcon, Video, FileText, File } from "lucide-react";

const DashboardCard = ({
  type,
  files,
}: {
  type: fileType;
  files: FileInfo[];
}) => {
  const fileIconName = getFileIconName({ type });
  const fileColor = getFileColor(type);
  const filteredFiles = files.filter((file) => file.type === type);
  const fileSize = filteredFiles.reduce((acc, file) => acc + file.size, 0);
  const size = getFileSize(fileSize);
  const fileCount = filteredFiles.length;

  // Get the appropriate Lucide icon component
  const getIconComponent = () => {
    switch (fileIconName) {
      case "ImageIcon":
        return <ImageIcon className="h-5 w-5 text-yellow-600" />;
      case "Video":
        return <Video className="h-5 w-5 text-red-600" />;
      case "FileText":
        return <FileText className="h-5 w-5 text-blue-600" />;
      default:
        return <File className="h-5 w-5 text-gray-600" />;
    }
  };

  const typeLabels = {
    images: "Images",
    videos: "Videos",
    documents: "Documents",
    others: "Other Files",
  };

  return (
    <div className="group rounded-lg border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-gray-300 hover:shadow-sm">
      {/* Icon and file count */}
      <div className="mb-3 flex items-start justify-between">
        <div
          className={`flex h-10 w-10 items-center justify-center rounded-lg ${fileColor} transition-transform duration-200 group-hover:scale-105`}
        >
          {getIconComponent()}
        </div>
        <div className="text-right">
          <div className="text-xl font-bold text-gray-900">{fileCount}</div>
          <div className="text-xs text-gray-500">files</div>
        </div>
      </div>

      {/* File type and size */}
      <div className="mb-2">
        <h3 className="text-sm font-medium text-gray-900 capitalize">
          {typeLabels[type]}
        </h3>
        <p className="text-xs text-gray-600">{size}</p>
      </div>

      {/* Progress bar */}
      <div className="mt-3">
        <div className="h-1 w-full rounded-full bg-gray-200">
          <div
            className={`h-1 rounded-full transition-all duration-300 ${
              type === "images"
                ? "bg-yellow-500"
                : type === "videos"
                  ? "bg-red-500"
                  : type === "documents"
                    ? "bg-blue-500"
                    : "bg-gray-500"
            }`}
            style={{
              width:
                fileCount > 0
                  ? `${Math.min((fileCount / Math.max(files.length, 1)) * 100, 100)}%`
                  : "0%",
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default DashboardCard;
