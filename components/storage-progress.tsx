"use client";

import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { calculatePercentage, getTotalStorage, getFileSize } from "@/lib/utils";
import { FileInfo } from "@/app/types";

export const StorageProgress = ({ files }: { files: FileInfo[] }) => {
  const used = getTotalStorage(files);
  const size = getFileSize(used);
  const percentage = calculatePercentage(used);
  const totalSizeGB = 2; // 2GB total storage
  const totalSizeBytes = totalSizeGB * 1024 * 1024 * 1024;
  const remainingBytes = totalSizeBytes - used;
  const remainingSize = getFileSize(remainingBytes);

  return (
    <Card className="overflow-hidden border-0 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 text-white shadow-xl">
      <CardContent className="p-8">
        {/* Header */}
        <div className="mb-8 text-center">
          <CardTitle className="mb-2 text-2xl font-bold">
            Storage Overview
          </CardTitle>
          <CardDescription className="text-blue-100">
            Monitor your storage usage and available space
          </CardDescription>
        </div>

        {/* Storage Stats */}
        <div className="mb-8 grid grid-cols-3 gap-6 text-center">
          <div className="rounded-xl bg-white/10 p-4 backdrop-blur-sm">
            <div className="text-2xl font-bold">{files.length}</div>
            <div className="text-sm text-blue-100">Total Files</div>
          </div>
          <div className="rounded-xl bg-white/10 p-4 backdrop-blur-sm">
            <div className="text-2xl font-bold">{size}</div>
            <div className="text-sm text-blue-100">Used Space</div>
          </div>
          <div className="rounded-xl bg-white/10 p-4 backdrop-blur-sm">
            <div className="text-2xl font-bold">{percentage.toFixed(1)}%</div>
            <div className="text-sm text-blue-100">Storage Used</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Storage Usage</span>
            <span className="text-blue-100">
              {size} / {totalSizeGB}GB
            </span>
          </div>

          {/* Main Progress Bar */}
          <div className="relative h-4 overflow-hidden rounded-full bg-white/20 backdrop-blur-sm">
            <div
              className="h-full bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 transition-all duration-1000 ease-out"
              style={{ width: `${Math.min(percentage, 100)}%` }}
            >
              {/* Animated shine effect */}
              <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
            </div>

            {/* Progress indicator dot */}
            <div
              className="absolute top-1/2 h-6 w-6 -translate-y-1/2 rounded-full bg-white shadow-lg transition-all duration-1000 ease-out"
              style={{ left: `calc(${Math.min(percentage, 100)}% - 12px)` }}
            >
              <div className="absolute inset-1 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
            </div>
          </div>

          {/* Storage Breakdown */}
          <div className="mt-6 grid grid-cols-2 gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-green-400"></div>
              <span className="text-blue-100">Available: {remainingSize}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-purple-400"></div>
              <span className="text-blue-100">Used: {size}</span>
            </div>
          </div>

          {/* Warning if storage is getting full */}
          {percentage > 80 && (
            <div className="mt-4 rounded-lg bg-yellow-500/20 p-3 backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <svg
                  className="h-4 w-4 text-yellow-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <span className="text-sm font-medium text-yellow-100">
                  Storage is {percentage > 90 ? "almost full" : "getting full"}
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
