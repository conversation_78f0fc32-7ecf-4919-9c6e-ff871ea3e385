"use client";

import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { calculatePercentage, getTotalStorage, getFileSize } from "@/lib/utils";
import { FileInfo } from "@/app/types";
import { AlertTriangle } from "lucide-react";

export const StorageProgress = ({ files }: { files: FileInfo[] }) => {
  const used = getTotalStorage(files);
  const size = getFileSize(used);
  const percentage = calculatePercentage(used);
  const totalSizeGB = 2; // 2GB total storage
  const totalSizeBytes = totalSizeGB * 1024 * 1024 * 1024;
  const remainingBytes = totalSizeBytes - used;
  const remainingSize = getFileSize(remainingBytes);

  return (
    <Card className="overflow-hidden border border-gray-200 bg-white shadow-sm">
      <CardContent className="p-8">
        {/* Header */}
        <div className="mb-8 text-center">
          <CardTitle className="mb-2 text-2xl font-bold text-gray-900">
            Storage Overview
          </CardTitle>
          <CardDescription className="text-gray-600">
            Monitor your storage usage and available space
          </CardDescription>
        </div>

        {/* Storage Stats */}
        <div className="mb-8 grid grid-cols-3 gap-6 text-center">
          <div className="rounded-xl border border-gray-100 bg-gray-50 p-4">
            <div className="text-2xl font-bold text-gray-900">
              {files.length}
            </div>
            <div className="text-sm text-gray-600">Total Files</div>
          </div>
          <div className="rounded-xl border border-gray-100 bg-gray-50 p-4">
            <div className="text-2xl font-bold text-gray-900">{size}</div>
            <div className="text-sm text-gray-600">Used Space</div>
          </div>
          <div className="rounded-xl border border-gray-100 bg-gray-50 p-4">
            <div className="text-2xl font-bold text-gray-900">
              {percentage.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Storage Used</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium text-gray-900">Storage Usage</span>
            <span className="text-gray-600">
              {size} / {totalSizeGB}GB
            </span>
          </div>

          {/* Main Progress Bar */}
          <div className="relative h-4 overflow-hidden rounded-full bg-gray-200">
            <div
              className="h-full bg-blue-500 transition-all duration-1000 ease-out"
              style={{ width: `${Math.min(percentage, 100)}%` }}
            ></div>

            {/* Progress indicator dot */}
            <div
              className="absolute top-1/2 h-6 w-6 -translate-y-1/2 rounded-full border-2 border-blue-500 bg-white shadow-lg transition-all duration-1000 ease-out"
              style={{ left: `calc(${Math.min(percentage, 100)}% - 12px)` }}
            >
              <div className="absolute inset-1 rounded-full bg-blue-500"></div>
            </div>
          </div>

          {/* Storage Breakdown */}
          <div className="mt-6 grid grid-cols-2 gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-green-500"></div>
              <span className="text-gray-600">Available: {remainingSize}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-blue-500"></div>
              <span className="text-gray-600">Used: {size}</span>
            </div>
          </div>

          {/* Warning if storage is getting full */}
          {percentage > 80 && (
            <div className="mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">
                  Storage is {percentage > 90 ? "almost full" : "getting full"}
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
