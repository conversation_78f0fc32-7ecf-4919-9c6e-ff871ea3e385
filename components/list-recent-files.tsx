"use client";
import { FileInfo } from "@/app/types";
import RecentFiles from "./recent-files";

const ListRecentFiles = ({ files }: { files: FileInfo[] }) => {
  const recentUploads = files
    .slice(0, 3)
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

  return (
    <div>
      {recentUploads.map((file) => (
        <RecentFiles key={file.id} file={file} />
      ))}
    </div>
  );
};

export default ListRecentFiles;
