"use client";
import { useState, useCallback } from "react";
import { toast } from "sonner";
import { useDropzone } from "react-dropzone";
import { useSession } from "@/lib/auth-client";
import { SignedUrl } from "@/app/actions/serve-actions";

const FileUpload = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);

  const userId = useSession()?.data?.user.id;

  const handleUpload = useCallback(async () => {
    if (files.length === 0) {
      toast.error("Please select at least one file to upload");
      return;
    }
    setUploading(true);
    try {
      for (const file of files) {
        const maxSizeMB = 5;
        if (file.size > maxSizeMB * 1024 * 1024) {
          toast.error(`File "${file.name}" exceeds ${maxSizeMB}MB limit`);
          continue;
        }
        const signedUrlResult = await SignedUrl();
        if (signedUrlResult.error) {
          throw new Error(signedUrlResult.error.message);
        }
        const url = signedUrlResult.success?.url;
        await fetch(url, {
          method: "PUT",
          body: file,
          headers: {
            "Content-Type": file.type,
          },
        });
      }
      toast.success("Files uploaded successfully");
      setFiles([]);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setUploading(false);
    }
  }, [files, userId]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
  });

  return (
    <div className="w-full">
      <div
        {...getRootProps()}
        className={`relative cursor-pointer rounded-xl border-2 border-dashed p-6 text-center transition-all duration-300 ${
          isDragActive
            ? "scale-105 border-blue-400 bg-blue-50"
            : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
        }`}
      >
        <input {...getInputProps()} multiple />
        <div className="flex flex-col items-center gap-3">
          <div
            className={`rounded-full p-3 ${isDragActive ? "bg-blue-100" : "bg-white"} shadow-sm`}
          >
            <svg
              className={`h-8 w-8 ${isDragActive ? "text-blue-600" : "text-gray-400"}`}
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
          </div>

          {isDragActive ? (
            <div>
              <p className="font-semibold text-blue-600">Drop the files here</p>
              <p className="text-sm text-blue-500">Release to upload</p>
            </div>
          ) : (
            <div>
              <p className="font-medium text-gray-700">
                Drag & drop files here
              </p>
              <p className="text-sm text-gray-500">
                or{" "}
                <span className="font-medium text-blue-600">
                  click to browse
                </span>
              </p>
              <p className="mt-1 text-xs text-gray-400">
                Maximum file size: 5MB
              </p>
            </div>
          )}
        </div>
      </div>

      {files.length > 0 && (
        <div className="mt-4 space-y-3">
          <div className="rounded-lg border border-gray-200 bg-white p-3">
            <h4 className="mb-2 text-sm font-medium text-gray-900">
              Selected Files ({files.length})
            </h4>
            <ul className="max-h-32 space-y-1 overflow-y-auto">
              {files.map((file) => (
                <li
                  key={file.name}
                  className="flex items-center justify-between text-xs"
                >
                  <span className="mr-2 flex-1 truncate text-gray-600">
                    {file.name}
                  </span>
                  <span className="flex-shrink-0 text-gray-400">
                    {(file.size / 1024 / 1024).toFixed(1)}MB
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <button
            onClick={(e) => {
              e.stopPropagation();
              handleUpload();
            }}
            disabled={uploading}
            className="w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3 font-semibold text-white shadow-sm transition-all duration-200 hover:from-blue-600 hover:to-blue-700 hover:shadow-md disabled:cursor-not-allowed disabled:opacity-50"
          >
            {uploading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                <span>Uploading...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <span>Upload Files</span>
              </div>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
