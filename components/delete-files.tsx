"use client";
import { deleteFile } from "@/app/actions/userActions";
import { toast } from "sonner";

const DeleteFiles = ({
  name,
  onDelete,
}: {
  name: string;
  onDelete: () => void;
}) => {
  const handleDelete = async (name: string) => {
    try {
      await deleteFile(name);
      toast.success(`File "${name}" deleted successfully`);
      onDelete(); // Call the onDelete callback to refresh the file list
    } catch (error) {
      toast.error(
        `Failed to delete file "${name}": ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  return (
    <div>
      <button onClick={() => handleDelete(name)}>Delete</button>
    </div>
  );
};

export default DeleteFiles;
