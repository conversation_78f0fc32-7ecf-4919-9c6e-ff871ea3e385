"use client";
import { FileInfo, fileType } from "@/app/types";
import { useState } from "react";
import { listFiles } from "@/app/actions/userActions";
import { getFileIcon, getFileColor, getFileSize } from "@/lib/utils";
import Image from "next/image";
import OptionsMenu from "./options-menu";

const ListFiles = ({
  initialFiles,
  userId,
  fileType,
}: {
  initialFiles: FileInfo[];
  userId: string;
  fileType: fileType;
}) => {
  const [files, setFiles] = useState<FileInfo[]>(initialFiles);

  const refetchFiles = async () => {
    const updatedFiles = (await listFiles(userId)).filter(
      (file) => file.type === fileType,
    );
    setFiles(updatedFiles);
  };

  const typeLabels = {
    images: "Images",
    videos: "Videos",
    documents: "Documents",
    others: "Other Files",
  };

  return (
    <div>
      {files.length === 0 ? (
        <div className="py-16 text-center">
          <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
            <svg
              className="h-10 w-10 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-xl font-semibold text-gray-900">
            No {typeLabels[fileType].toLowerCase()} yet
          </h3>
          <p className="mb-6 text-gray-500">
            Upload your first{" "}
            {fileType === "others" ? "file" : fileType.slice(0, -1)} to get
            started
          </p>
          <div className="inline-flex items-center gap-2 text-sm text-gray-400">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
            <span>Use the upload area on the left to add files</span>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {files.map((file) => (
            <div
              key={file.id}
              className="group relative rounded-lg border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-gray-300 hover:shadow-sm"
            >
              {/* File Icon */}
              <div className="mb-3 flex items-start justify-between">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-lg ${getFileColor(file.type)} transition-transform duration-200 group-hover:scale-105`}
                >
                  <Image
                    src={getFileIcon({ type: file.type })}
                    alt="file-icon"
                    width={20}
                    height={20}
                  />
                </div>
                <OptionsMenu name={file.path} onDelete={refetchFiles} />
              </div>

              {/* File Details */}
              <div className="space-y-1">
                <h3
                  className="truncate text-sm font-medium text-gray-900"
                  title={file.name}
                >
                  {file.name}
                </h3>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 capitalize">{file.type}</span>
                  <span className="font-medium text-gray-700">
                    {getFileSize(file.size)}
                  </span>
                </div>
                <p className="text-xs text-gray-400">
                  {new Date(file.createdAt).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
export default ListFiles;
