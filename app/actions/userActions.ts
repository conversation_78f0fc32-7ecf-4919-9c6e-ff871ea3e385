"use server"

import { storage } from "@/firebase/firebase";
import { FileInfo } from "@/app/types";
import { ref, uploadBytes, deleteObject, getDownloadURL } from "firebase/storage";
import { db } from "@/db";
import { files } from "@/db/schema";
import { randomUUID } from "crypto";
import { eq } from "drizzle-orm";
import { getFileType } from "@/lib/utils";

export const uploadFile = async (file: File, path: string, userId?: string): Promise<string | null> => {
    if (!userId) {
        throw new Error("userId is required to upload a file.");
    }
    const name = file.name.replace(/\s+/g, '_');
    const storageRef = ref(storage, path);
    const fileSize = file.size;

    await uploadBytes(storageRef, file);
    // Save metadata to DB
    await db.insert(files).values({
        id: randomUUID(),
        name: name,
        path: path,
        createdAt: new Date(),
        userId: userId,
        size: fileSize,
    });

    return path;
};

export const deleteFile = async (filePath: string): Promise<void> => {
    await deleteObject(ref(storage, filePath));
    await db.delete(files).where(eq(files.path, filePath));
};


export const listFiles = async (userId: string|undefined): Promise<FileInfo[]> => {
    if (userId) {
        const filesData = await db.select().from(files).where(eq(files.userId, userId));
        return filesData.map((file) => {
            const path = file.path;
            const name = file.name.includes(".") ? file.name.substring(0, file.name.lastIndexOf(".")) : file.name;
            const extension = file.name.includes(".") ? file.name.split(".").pop() || "" : "";
            const type = getFileType(extension);
            const id = file.id;
            const createdAt = file.createdAt;
            const userId = file.userId;
            return { id, name, extension, path, createdAt, userId, type, size: file.size };
        });
    }
    return [];
};

export const downloadFile = async (filePath: string): Promise<string> => {
    const fileRef = ref(storage, filePath);
    const downloadUrl = await getDownloadURL(fileRef);
    return downloadUrl;
};

// export const editFileName = async (oldFilePath: string, newFileName: string): Promise<string> => {
    
// };


