"use server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3"
import { getSignedUrl } from "@aws-sdk/s3-request-presigner"

const r2 = new S3Client({
    endpoint: process.env.R2_ENDPOINT,
    region: process.env.R2_REGION,
    credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
    },
})
export const SignedUrl = async () => {
    const session = await auth.api.getSession({
        headers: await headers(),
    });
    if (!session) {
        return { error: { message: "not logged in" } }
    }
    const putObject = new PutObjectCommand({
        Bucket: process.env.R2_BUCKET_NAME!,
        Key: `uploads/user/${session.user.id}/file/${Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`,
    })
    const url = await getSignedUrl(r2, putObject, {
        expiresIn: 60,
    })

    return { success: { url: url } }
}