import React from "react";

function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-gray-100">
      {/* Background Pattern */}
      <div className="bg-grid-pattern absolute inset-0 opacity-5"></div>

      {/* Animated Background Elements */}
      <div className="animate-float absolute top-20 left-10 h-20 w-20 rounded-full bg-gradient-to-r from-blue-200 to-blue-300 opacity-30 blur-sm"></div>
      <div className="animate-float-delayed absolute top-40 right-20 h-16 w-16 rounded-full bg-gradient-to-r from-purple-200 to-purple-300 opacity-30 blur-sm"></div>
      <div className="animate-float absolute bottom-20 left-20 h-24 w-24 rounded-full bg-gradient-to-r from-green-200 to-green-300 opacity-30 blur-sm"></div>
      <div className="animate-float-delayed absolute right-10 bottom-40 h-12 w-12 rounded-full bg-gradient-to-r from-yellow-200 to-yellow-300 opacity-30 blur-sm"></div>

      {/* Additional decorative elements */}
      <div className="absolute top-1/4 left-1/4 h-32 w-32 animate-pulse rounded-full bg-gradient-to-r from-indigo-100 to-indigo-200 opacity-20 blur-lg"></div>
      <div className="absolute right-1/4 bottom-1/4 h-28 w-28 animate-pulse rounded-full bg-gradient-to-r from-pink-100 to-pink-200 opacity-20 blur-lg delay-1000"></div>

      {/* Main Content */}
      <div className="relative flex min-h-screen items-center justify-center p-4">
        <div className="mx-auto w-full max-w-7xl">
          {/* Content wrapper with subtle backdrop */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-white/20 backdrop-blur-sm"></div>
            <div className="relative z-10">{children}</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-4 left-1/2 z-20 -translate-x-1/2 transform">
        <div className="flex items-center gap-4 text-xs text-gray-500">
          <p>© {new Date().getFullYear()} SafeSpace</p>
          <span>•</span>
          <a href="/privacy" className="transition-colors hover:text-gray-700">
            Privacy
          </a>
          <span>•</span>
          <a href="/terms" className="transition-colors hover:text-gray-700">
            Terms
          </a>
          <span>•</span>
          <a href="/support" className="transition-colors hover:text-gray-700">
            Support
          </a>
        </div>
      </div>
    </div>
  );
}

export default Layout;
