import GetFiles from "@/components/get-files";
import FileUpload from "@/components/file-upload";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { listFiles } from "@/app/actions/userActions";
import { getTotalStorage, getFileSize } from "@/lib/utils";

const page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;
  const files = await listFiles(userId);
  const documentFiles = files.filter((file) => file.type === "documents");
  const totalSize = getTotalStorage(documentFiles);
  const formattedSize = getFileSize(totalSize);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="mb-6 flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-purple-100">
              <svg
                className="h-6 w-6 text-purple-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
              <p className="text-sm text-gray-600">
                {documentFiles.length} files • {formattedSize}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-5">
          {/* Upload Section */}
          <div className="xl:col-span-2">
            <div className="rounded-lg border border-gray-200 bg-white p-6">
              <h2 className="mb-4 text-lg font-medium text-gray-900">
                Upload Documents
              </h2>
              <FileUpload />
            </div>
          </div>

          {/* Files Section */}
          <div className="xl:col-span-3">
            <div className="rounded-lg border border-gray-200 bg-white p-6">
              <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900">
                  Your Documents
                </h2>
              </div>
              <GetFiles type="documents" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
