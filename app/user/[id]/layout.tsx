import { AppSidebar } from "@/components/app-sidebar";
import NavBar from "@/components/NavBar";
import { SidebarProvider } from "@/components/ui/sidebar";
import React from "react";
import { Toaster } from "@/components/ui/sonner";

const layout = ({ children }: Readonly<{ children: React.ReactNode }>) => {
  return (
    <SidebarProvider>
      <main className="flex h-screen w-screen bg-gray-50">
        <AppSidebar />
        <div className="flex flex-1 flex-col overflow-hidden">
          <NavBar />
          <div className="flex-1 overflow-auto">{children}</div>
          <Toaster />
        </div>
      </main>
    </SidebarProvider>
  );
};

export default layout;
