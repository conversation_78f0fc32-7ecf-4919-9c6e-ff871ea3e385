import GetFiles from "@/components/get-files";
import FileUpload from "@/components/file-upload";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { listFiles } from "@/app/actions/userActions";
import { getTotalStorage, getFileSize } from "@/lib/utils";

const page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;
  const files = await listFiles(userId);
  const videoFiles = files.filter((file) => file.type === "videos");
  const totalSize = getTotalStorage(videoFiles);
  const formattedSize = getFileSize(totalSize);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="mb-6 flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Videos</h1>
              <p className="text-sm text-gray-600">
                {videoFiles.length} files • {formattedSize}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-5">
          {/* Upload Section */}
          <div className="xl:col-span-2">
            <div className="rounded-lg border border-gray-200 bg-white p-6">
              <h2 className="mb-4 text-lg font-medium text-gray-900">
                Upload Videos
              </h2>
              <FileUpload />
            </div>
          </div>

          {/* Files Section */}
          <div className="xl:col-span-3">
            <div className="rounded-lg border border-gray-200 bg-white p-6">
              <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900">
                  Your Videos
                </h2>
              </div>
              <GetFiles type="videos" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
