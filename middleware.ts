import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
export const config = {
    matcher: [
        '/user/:path*',
    ],
};

export const middleware = async (request: Request) => {
    const session = await auth.api.getSession({ headers: request.headers });

    if (!session) {
        return NextResponse.redirect(new URL('/sign-in', request.url));
    }

    // Extract the requested path
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');

    // Check if we're in the user/[id] route
    if (pathSegments[1] === 'user' && pathSegments.length >= 3) {
        const requestedUserId = pathSegments[2];
        const currentUserId = session.user.id;

        // If trying to access another user's page, redirect to their own
        if (requestedUserId !== currentUserId) {
            return NextResponse.redirect(new URL(`/user/${currentUserId}${pathSegments.slice(3).join('/')}`, request.url));
        }
    }

    return NextResponse.next();
};
